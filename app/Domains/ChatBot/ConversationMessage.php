<?php

namespace App\Domains\ChatBot;

use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Domains\User;
use App\Domains\WhatsAppWebhookLog;
use Carbon\Carbon;

class ConversationMessage
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $client_id;
    public ?int $user_id;
    public ?int $phone_number_id;
    public ?int $conversation_id;
    public ?int $webhook_log_id;
    public ?int $message_id;
    public bool $inbound;
    public bool $outbound;
    public string $message;
    public ?Carbon $sent_at;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Carbon $deleted_at;

    // Relationships
    public ?Organization $organization;
    public ?Client $client;
    public ?User $user;
    public ?PhoneNumber $phone_number;
    public ?Conversation $conversation;
    public ?WhatsAppWebhookLog $webhook_log;
    public ?Message $message_relation;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $client_id,
        ?int $user_id,
        ?int $phone_number_id,
        ?int $conversation_id,
        ?int $webhook_log_id,
        ?int $message_id,
        bool $inbound = false,
        bool $outbound = false,
        string $message = '',
        ?Carbon $sent_at = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Carbon $deleted_at = null,
        ?Organization $organization = null,
        ?Client $client = null,
        ?User $user = null,
        ?PhoneNumber $phone_number = null,
        ?Conversation $conversation = null,
        ?WhatsAppWebhookLog $webhook_log = null,
        ?Message $message_relation = null,
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->client_id = $client_id;
        $this->user_id = $user_id;
        $this->phone_number_id = $phone_number_id;
        $this->conversation_id = $conversation_id;
        $this->webhook_log_id = $webhook_log_id;
        $this->message_id = $message_id;
        $this->inbound = $inbound;
        $this->outbound = $outbound;
        $this->message = $message;
        $this->sent_at = $sent_at;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->deleted_at = $deleted_at;
        $this->organization = $organization;
        $this->client = $client;
        $this->user = $user;
        $this->phone_number = $phone_number;
        $this->conversation = $conversation;
        $this->webhook_log = $webhook_log;
        $this->message_relation = $message_relation;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'client_id' => $this->client_id,
            'user_id' => $this->user_id,
            'phone_number_id' => $this->phone_number_id,
            'conversation_id' => $this->conversation_id,
            'webhook_log_id' => $this->webhook_log_id,
            'message_id' => $this->message_id,
            'inbound' => $this->inbound,
            'outbound' => $this->outbound,
            'message' => $this->message,
            'direction' => $this->getDirection(),
            'sent_at' => $this->sent_at?->format('Y-m-d H:i:s'),
            'created_at' => ($this->created_at ?? Carbon::now())->format('Y-m-d H:i:s'),
            'updated_at' => ($this->updated_at ?? Carbon::now())->format('Y-m-d H:i:s'),
            'deleted_at' => $this->deleted_at?->format('Y-m-d H:i:s'),
            'organization' => $this->organization?->toArray(),
            'client' => $this->client?->toArray(),
            'user' => $this->user?->toArray(),
            'phone_number' => $this->phone_number?->toArray(),
            'conversation' => $this->conversation?->toArray(),
            'webhook_log' => $this->webhook_log?->toArray(),
            'message_relation' => $this->message_relation?->toArray(),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            'organization_id' => $this->organization_id,
            'client_id' => $this->client_id,
            'user_id' => $this->user_id,
            'phone_number_id' => $this->phone_number_id,
            'conversation_id' => $this->conversation_id,
            'webhook_log_id' => $this->webhook_log_id,
            'message_id' => $this->message_id,
            'inbound' => $this->inbound,
            'outbound' => $this->outbound,
            'message' => $this->message,
            'sent_at' => $this->sent_at,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'user_id' => $this->user_id,
            'phone_number_id' => $this->phone_number_id,
            'conversation_id' => $this->conversation_id,
            'webhook_log_id' => $this->webhook_log_id,
            'message_id' => $this->message_id,
            'inbound' => $this->inbound,
            'outbound' => $this->outbound,
            'message' => $this->message,
            'sent_at' => $this->sent_at,
        ];
    }

    /**
     * Check if message is inbound
     */
    public function isInbound(): bool
    {
        return $this->inbound === true;
    }

    /**
     * Check if message is outbound
     */
    public function isOutbound(): bool
    {
        return $this->outbound === true;
    }

    /**
     * Get message direction as string
     */
    public function getDirection(): string
    {
        if ($this->isInbound()) {
            return 'inbound';
        }
        
        if ($this->isOutbound()) {
            return 'outbound';
        }
        
        return 'unknown';
    }

    /**
     * Create an inbound conversation message from webhook
     */
    public static function createInbound(
        int $organization_id,
        int $client_id,
        string $message,
        ?int $phone_number_id = null,
        ?int $conversation_id = null,
        ?int $webhook_log_id = null,
        ?Carbon $sent_at = null
    ): self {
        return new self(
            id: null,
            organization_id: $organization_id,
            client_id: $client_id,
            user_id: null,
            phone_number_id: $phone_number_id,
            conversation_id: $conversation_id,
            webhook_log_id: $webhook_log_id,
            message_id: null,
            inbound: true,
            outbound: false,
            message: $message,
            sent_at: $sent_at ?? Carbon::now()
        );
    }

    /**
     * Create an outbound conversation message from sent message
     */
    public static function createOutbound(
        int $organization_id,
        int $client_id,
        string $message,
        ?int $user_id = null,
        ?int $phone_number_id = null,
        ?int $conversation_id = null,
        ?int $message_id = null,
        ?Carbon $sent_at = null
    ): self {
        return new self(
            id: null,
            organization_id: $organization_id,
            client_id: $client_id,
            user_id: $user_id,
            phone_number_id: $phone_number_id,
            conversation_id: $conversation_id,
            webhook_log_id: null,
            message_id: $message_id,
            inbound: false,
            outbound: true,
            message: $message,
            sent_at: $sent_at ?? Carbon::now()
        );
    }
}
