<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Services\ASAAS\Models\AsaasClient;

class Client extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'clients';

    protected $fillable = [
        'organization_id',
        'name',
        'phone',
        'email',
        'profession',
        'birthdate',
        'cpf',
        'cnpj',
        'service',
        'address',
        'number',
        'neighborhood',
        'cep',
        'complement',
        'civil_state',
        'description',
    ];

    protected $casts = [
        'birthdate' => 'date',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function stock_entries(){
        return $this->hasMany(StockEntry::class);
    }
    public function stock_exits(){
        return $this->hasMany(StockExit::class);
    }
    public function sales(){
        return $this->hasMany(Sale::class);
    }
    public function campaigns(){
        return $this->belongsToMany(Campaign::class, 'campaigns_clients');
    }

    public function messages(){
        return $this->hasMany(Message::class);
    }

    /**
     * Get the ASAAS integration for this client
     */
    public function asaas(): HasOne
    {
        return $this->hasOne(AsaasClient::class);
    }

    /**
     * Check if client has ASAAS integration
     */
    public function hasAsaasIntegration(): bool
    {
        return $this->asaas !== null;
    }

    /**
     * Get ASAAS customer ID (convenience method)
     */
    public function getAsaasCustomerId(): ?string
    {
        return $this->asaas?->asaas_customer_id;
    }

    /**
     * Get document (CPF or CNPJ) for ASAAS
     */
    public function getDocument(): ?string
    {
        return $this->cpf ?: $this->cnpj;
    }

    /**
     * Get document attribute (accessor for compatibility)
     */
    public function getDocumentAttribute(): ?string
    {
        return $this->getDocument();
    }

    /**
     * Get mobile phone attribute (accessor for compatibility)
     */
    public function getMobilePhoneAttribute(): ?string
    {
        return $this->phone;
    }

    /**
     * Get birth date attribute (accessor for compatibility)
     */
    public function getBirthDateAttribute(): ?\Carbon\Carbon
    {
        return $this->attributes['birthdate'] ? \Carbon\Carbon::parse($this->attributes['birthdate']) : null;
    }

    /**
     * Get postal code attribute (accessor for compatibility)
     */
    public function getPostalCodeAttribute(): ?string
    {
        return $this->cep;
    }

    /**
     * Get address number attribute (accessor for compatibility)
     */
    public function getAddressNumberAttribute(): ?string
    {
        return $this->number;
    }

    /**
     * Get province attribute (accessor for compatibility)
     */
    public function getProvinceAttribute(): ?string
    {
        return $this->neighborhood;
    }

    /**
     * Check if client needs ASAAS sync
     */
    public function needsAsaasSync(): bool
    {
        return $this->asaas?->needsSync() ?? false;
    }

    /**
     * Mark client as synced (for compatibility)
     */
    public function markAsSynced(): void
    {
        if ($this->asaas) {
            $this->asaas->markSyncSuccess();
        }
    }
}
