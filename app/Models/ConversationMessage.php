<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ConversationMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'conversation_messages';

    protected $fillable = [
        'organization_id',
        'client_id',
        'user_id',
        'phone_number_id',
        'conversation_id',
        'webhook_log_id',
        'message_id',
        'inbound',
        'outbound',
        'message',
        'sent_at',
    ];

    protected $casts = [
        'inbound' => 'boolean',
        'outbound' => 'boolean',
        'sent_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the organization that owns the conversation message
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the client that owns the conversation message
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the user that owns the conversation message
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the phone number associated with the conversation message
     */
    public function phoneNumber(): BelongsTo
    {
        return $this->belongsTo(PhoneNumber::class);
    }

    /**
     * Get the conversation that owns the conversation message
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }

    /**
     * Get the webhook log associated with the conversation message
     */
    public function webhookLog(): BelongsTo
    {
        return $this->belongsTo(WhatsAppWebhookLog::class, 'webhook_log_id');
    }

    /**
     * Get the message associated with the conversation message
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    /**
     * Scope for inbound messages
     */
    public function scopeInbound($query)
    {
        return $query->where('inbound', true);
    }

    /**
     * Scope for outbound messages
     */
    public function scopeOutbound($query)
    {
        return $query->where('outbound', true);
    }

    /**
     * Scope for filtering by conversation
     */
    public function scopeByConversation($query, int $conversationId)
    {
        return $query->where('conversation_id', $conversationId);
    }

    /**
     * Scope for filtering by client
     */
    public function scopeByClient($query, int $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Scope for filtering by organization
     */
    public function scopeByOrganization($query, int $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Check if message is inbound
     */
    public function isInbound(): bool
    {
        return $this->inbound === true;
    }

    /**
     * Check if message is outbound
     */
    public function isOutbound(): bool
    {
        return $this->outbound === true;
    }

    /**
     * Get message direction as string
     */
    public function getDirection(): string
    {
        if ($this->isInbound()) {
            return 'inbound';
        }
        
        if ($this->isOutbound()) {
            return 'outbound';
        }
        
        return 'unknown';
    }
}
