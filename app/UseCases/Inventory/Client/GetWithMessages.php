<?php

namespace App\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Repositories\ClientRepository;
use App\Repositories\MessageRepository;
use Exception;

class GetWithMessages
{
    private ClientRepository $clientRepository;
    private MessageRepository $messageRepository;

    public function __construct(
        ClientRepository $clientRepository,
        MessageRepository $messageRepository
    ) {
        $this->clientRepository = $clientRepository;
        $this->messageRepository = $messageRepository;
    }

    /**
     * @param int $id
     * @return array
     * @throws Exception
     */
    public function perform(int $id) : array {
        $organization_id = request()->user()->organization_id;

        $client = $this->clientRepository->fetchById($id);

        if($client->organization_id !== $organization_id){
            throw new Exception(
                "This client don't belong to this organization." ,
                403
            );
        }

        // Buscar mensagens do cliente em ordem decrescente por id
        $messages = $this->messageRepository->fetchByClientId($id, $organization_id);

        return [
            'client' => $client->toArray(),
            'messages' => array_map(fn($message) => $message->toArray(), $messages)
        ];
    }
}
