<?php

namespace App\Repositories;

use App\Domains\ChatBot\Message as MessageDomain;
use App\Domains\Filters\MessageFilters;
use App\Domains\Filters\OrderBy;
use App\Enums\MessageStatus;
use App\Factories\ChatBot\MessageFactory;
use App\Models\Message;
use EloquentBuilder;

class MessageRepository
{
    private MessageFactory $messageFactory;

    public function __construct(MessageFactory $messageFactory){
        $this->messageFactory = $messageFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(MessageFilters $filters, OrderBy $orderBy) : array {
        $messages = [];

        $models = EloquentBuilder::to(Message::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model);
        }

        return [
            'data' => $messages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, MessageFilters $filters, OrderBy $orderBy) : array {
        $messages = [];

        $models = EloquentBuilder::to(Message::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model);
        }

        return [
            'data' => $messages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, MessageFilters $filters): int {
        return EloquentBuilder::to(Message::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, MessageFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Message::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(MessageDomain $message) : MessageDomain {
        $savedMessage = Message::create($message->toStoreArray());

        $message->id = $savedMessage->id;

        return $message;
    }

    public function update(MessageDomain $message, int $organization_id) : MessageDomain {
        Message::where('id', $message->id)
            ->where('organization_id', $organization_id)
            ->update($message->toUpdateArray());

        return $message;
    }

    public function save(MessageDomain $message, int $organization_id) : MessageDomain {
        if($message->id){
            return $this->update($message, $organization_id);
        }
        return $this->store($message);
    }

    public function fetchById(int $id) : MessageDomain {
        return $this->messageFactory->buildFromModel(
            Message::findOrFail($id), true
        );
    }

    public function fetchByBot(string $bot) : MessageDomain {
        return $this->messageFactory->buildFromModel(
            Message::where('bot', $bot)->first()
        );
    }

    public function delete(MessageDomain $message) : bool {
        return Message::find($message->id)->delete();
    }

    /**
     * Fetch all messages available to send across organizations
     *
     * @param int $limit
     * @return array
     */
    public function fetchAllAvailableToSend(int $limit) : array {
        $messages = [];

        $models = Message::with(['client', 'template', 'campaign'])
            ->where("status", MessageStatus::is_sending->value)
            ->where("is_sent", false)
            ->where( function ($query) {
                $query->whereNull("scheduled_at")->orWhere("scheduled_at", "<=", now());
            })
            ->orderBy("created_at", "ASC")
            ->limit($limit)
            ->get();

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model, true);
        }

        return $messages;
    }

    public function fetchByCampaignAndClient(int $campaign_id, int $client_id) : ?MessageDomain {
        return $this->messageFactory->buildFromModel(
            Message::where("campaign_id", $campaign_id)
                ->where("client_id", $client_id)
                ->where("is_sent", false)
                ->first(), true
        );
    }

    /**
     * Fetch messages by client ID in descending order
     * @param int $client_id
     * @param int $organization_id
     * @return array
     */
    public function fetchByClientId(int $client_id, int $organization_id) : array {
        $messages = [];

        $models = Message::where("client_id", $client_id)
            ->where("organization_id", $organization_id)
            ->orderBy('id', 'desc')
            ->get();

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model, true);
        }

        return $messages;
    }

    /**
     * Get messages ready for retry
     */
    public function getMessagesReadyForRetry(int $limit = 100): array
    {
        $models = Message::where('status', MessageStatus::is_failed)
                         ->where('delivery_attempts', '<', \DB::raw('max_retries'))
                         ->where(function($query) {
                             $query->whereNull('next_retry_at')
                                   ->orWhere('next_retry_at', '<=', now());
                         })
                         ->orderBy('next_retry_at')
                         ->limit($limit)
                         ->get();

        return $this->messageFactory->buildFromModels($models);
    }

    /**
     * Get failed messages by campaign
     */
    public function getFailedMessagesByCampaign(int $campaign_id): array
    {
        $models = Message::where('campaign_id', $campaign_id)
                         ->where('status', MessageStatus::is_failed)
                         ->with(['client', 'deliveryAttempts'])
                         ->orderBy('last_attempt_at', 'desc')
                         ->get();

        return $this->messageFactory->buildFromModels($models);
    }

    /**
     * Get messages that have failed permanently
     */
    public function getPermanentlyFailedMessages(int $campaign_id = null): array
    {
        $query = Message::where('status', MessageStatus::is_failed)
                        ->where('delivery_attempts', '>=', \DB::raw('max_retries'));

        if ($campaign_id) {
            $query->where('campaign_id', $campaign_id);
        }

        $models = $query->with(['client', 'campaign'])
                       ->orderBy('last_attempt_at', 'desc')
                       ->get();

        return $this->messageFactory->buildFromModels($models);
    }

    /**
     * Get message statistics for a campaign
     */
    public function getCampaignMessageStatistics(int $campaign_id): array
    {
        $stats = Message::where('campaign_id', $campaign_id)
                        ->selectRaw('
                            COUNT(*) as total,
                            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as sent,
                            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed,
                            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as draft,
                            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as sending,
                            SUM(CASE WHEN delivery_attempts >= max_retries AND status = ? THEN 1 ELSE 0 END) as permanently_failed,
                            AVG(delivery_attempts) as avg_delivery_attempts
                        ', [
                            MessageStatus::is_sent->value,
                            MessageStatus::is_failed->value,
                            MessageStatus::is_draft->value,
                            MessageStatus::is_sending->value,
                            MessageStatus::is_failed->value,
                        ])
                        ->first();

        return [
            'total' => $stats->total ?? 0,
            'sent' => $stats->sent ?? 0,
            'failed' => $stats->failed ?? 0,
            'draft' => $stats->draft ?? 0,
            'sending' => $stats->sending ?? 0,
            'permanently_failed' => $stats->permanently_failed ?? 0,
            'pending' => ($stats->total ?? 0) - ($stats->sent ?? 0) - ($stats->permanently_failed ?? 0),
            'avg_delivery_attempts' => round($stats->avg_delivery_attempts ?? 0, 2),
            'success_rate' => $stats->total > 0 ? round(($stats->sent / $stats->total) * 100, 2) : 0,
        ];
    }

    /**
     * Reset message for retry (clear retry state)
     */
    public function resetMessageForRetry(int $message_id): bool
    {
        return Message::where('id', $message_id)
                      ->update([
                          'delivery_attempts' => 0,
                          'last_attempt_at' => null,
                          'next_retry_at' => null,
                          'last_error_message' => null,
                          'status' => MessageStatus::is_draft->value,
                          'is_fail' => false,
                      ]) > 0;
    }

    /**
     * Bulk reset messages for retry
     */
    public function bulkResetMessagesForRetry(array $message_ids): int
    {
        return Message::whereIn('id', $message_ids)
                      ->update([
                          'delivery_attempts' => 0,
                          'last_attempt_at' => null,
                          'next_retry_at' => null,
                          'last_error_message' => null,
                          'status' => MessageStatus::is_draft->value,
                          'is_fail' => false,
                      ]);
    }

    public function countIsFailByCampaignId(int $campaign_id): int {
        return Message::where('campaign_id', $campaign_id)
             ->where('is_fail', true)
             ->count();
    }
    public function countIsDeliveredByCampaignId(int $campaign_id): int {
        return Message::where('campaign_id', $campaign_id)
             ->where('is_delivered', true)
             ->count();
    }
    public function countIsReadByCampaignId(int $campaign_id): int {
        return Message::where('campaign_id', $campaign_id)
             ->where('is_read', true)
             ->count();
    }
    public function countIsSentByCampaignId(int $campaign_id): int {
        return Message::where('campaign_id', $campaign_id)
             ->where('is_sent', true)
             ->count();
    }

    public function countIsSendingByCampaignId(int $campaign_id): int {
        return Message::where('campaign_id', $campaign_id)
             ->where('status', MessageStatus::is_sending->value)
             ->count();
    }

    public function countIsDraftByCampaignId(int $campaign_id): int {
        return Message::where('campaign_id', $campaign_id)
             ->where('status', MessageStatus::is_draft->value)
             ->count();
    }

    public function countByCampaignId(int $campaign_id): int {
        return Message::where('campaign_id', $campaign_id)
             ->count();
    }

    public function fetchByCampaignId(int $campaign_id) : array {
        $messages = [];
        $models = Message::where('campaign_id', $campaign_id)
            ->get();

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model);
        }

        return $messages;
    }

    public function fetchFailedByCampaignId(int $campaign_id) : array {
        $messages = [];
        $models = Message::where('campaign_id', $campaign_id)
            ->where('status', MessageStatus::is_failed->value)
            ->get();

        foreach ($models as $model){
            $messages[] = $this->messageFactory->buildFromModel($model);
        }

        return $messages;
    }
}
