<?php

namespace App\Repositories;

use App\Domains\ChatBot\Flow as FlowDomain;
use App\Domains\Filters\FlowFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\FlowFactory;
use App\Models\Flow;
use EloquentBuilder;

class FlowRepository
{
    private FlowFactory $flowFactory;

    public function __construct(FlowFactory $flowFactory){
        $this->flowFactory = $flowFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(FlowFilters $filters, OrderBy $orderBy) : array {
        $flows = [];

        $models = EloquentBuilder::to(Flow::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $flows[] = $this->flowFactory->buildFromModel($model);
        }

        return [
            'data' => $flows,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, FlowFilters $filters, OrderBy $orderBy) : array {
        $flows = [];

        $models = EloquentBuilder::to(Flow::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $flows[] = $this->flowFactory->buildFromModel($model);
        }

        return [
            'data' => $flows,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, FlowFilters $filters): int {
        return EloquentBuilder::to(Flow::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, FlowFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Flow::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(FlowDomain $flow) : FlowDomain {
        $savedFlow = Flow::create($flow->toStoreArray());

        $flow->id = $savedFlow->id;

        return $flow;
    }

    public function update(FlowDomain $flow, int $organization_id) : FlowDomain {
        Flow::where('id', $flow->id)
            ->where('organization_id', $organization_id)
            ->update($flow->toUpdateArray());

        return $flow;
    }

    public function save(FlowDomain $flow, int $organization_id) : FlowDomain {
        if($flow->id){
            return $this->update($flow, $organization_id);
        }
        return $this->store($flow);
    }

    public function fetchById(int $id) : FlowDomain {
        return $this->flowFactory->buildFromModel(
            Flow::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : FlowDomain {
        return $this->flowFactory->buildFromModel(
            Flow::where('bot', $bot)->first()
        );
    }

    public function fetchDefaultFlow(int $organization_id) : ?FlowDomain {
        return $this->flowFactory->buildFromModel(
            Flow::where('organization_id', $organization_id)
                ->where('is_default_flow', true)
                ->first()
        );
    }

    public function delete(FlowDomain $flow) : bool {
        return Flow::find($flow->id)->delete();
    }

    /**
     * Find default flow for organization
     */
    public function findDefaultFlowForOrganization(int $organizationId): ?FlowDomain
    {
        $model = Flow::where('organization_id', $organizationId)
            ->where('is_default_flow', true)
            ->with('steps')
            ->first();

        return $model ? $this->flowFactory->buildFromModel($model) : null;
    }
}
