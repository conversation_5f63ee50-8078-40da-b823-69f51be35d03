<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\BudgetController;
use App\Http\Controllers\BudgetProductController;
use App\Http\Controllers\ChatBot\ButtonController;
use App\Http\Controllers\ChatBot\CampaignController;
use App\Http\Controllers\ChatBot\CategoryController;
use App\Http\Controllers\ChatBot\ComponentController;
use App\Http\Controllers\ChatBot\ConversationController;
use App\Http\Controllers\ChatBot\FlowController;
use App\Http\Controllers\ChatBot\InteractionController;
use App\Http\Controllers\ChatBot\MessageController;
use App\Http\Controllers\ChatBot\ParameterController;
use App\Http\Controllers\ChatBot\PhoneNumberController;
use App\Http\Controllers\ChatBot\StepController;
use App\Http\Controllers\ChatBot\TagController;
use App\Http\Controllers\ChatBot\TemplateController;
use App\Http\Controllers\ChatBot\WhatsAppMessageController;
use App\Http\Controllers\ChatBot\WhatsAppSyncController;
use App\Http\Controllers\ChatBot\AnalyticsController;
use App\Http\Controllers\ChatBot\WhatsAppWebhookEntryController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\CustomProductController;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\DepartmentUserController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\GroupProductController;
use App\Http\Controllers\ImportController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\LogController;
use App\Http\Controllers\Meta\WhatsApp\WhatsAppWebhookController;
use App\Http\Controllers\WhatsAppWebhookLogController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductHistoryController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ProjectProductController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\ASAAS\AccountController as AsaasAccountController;
use App\Http\Controllers\ASAAS\CustomerController as AsaasCustomerController;
use App\Http\Controllers\ASAAS\PaymentController as AsaasPaymentController;
use App\Http\Controllers\ASAAS\SubscriptionController as AsaasSubscriptionController;
use App\Http\Controllers\ASAAS\Resources\AsaasOrganizationController as AsaasOrganizationResourceController;
use App\Http\Controllers\ASAAS\Resources\AsaasOrganizationCustomerController as AsaasOrganizationCustomerResourceController;
use App\Http\Controllers\ASAAS\Resources\AsaasClientController as AsaasClientResourceController;
use App\Http\Controllers\ASAAS\Resources\AsaasSaleController as AsaasSaleResourceController;
use App\Http\Controllers\ASAAS\Resources\AsaasSubscriptionController as AsaasSubscriptionResourceController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\StockEntryController;
use App\Http\Controllers\StockExitController;
use App\Http\Controllers\Telegram\TelegramBotController;
use App\Http\Controllers\Telegram\TelegramChatController;
use App\Http\Controllers\Telegram\TelegramController;
use App\Http\Controllers\Telegram\TelegramMessageController;
use App\Http\Controllers\Telegram\TelegramUserController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\Tesseract\RawImageController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
- build command to actually publish
- sent to meta
- check how build this template
- build command for campaign
- generate messages
- sent messages to meta
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider, and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/
Route::get('/give-my-php-info', function () {
    phpinfo();
});
Route::get('/give-my-php-v', function () {
    return response()->json(['php_version' => phpversion()]);
});

Route::post('/login', [AuthController::class, 'login']);
Route::post('/register', [AuthController::class, 'register']);

// Password reset routes (no auth required)
Route::prefix('auth')->group(function () {
    Route::post('password/forgot', [AuthController::class, 'forgotPassword'])->middleware('throttle:password-reset');
    Route::post('password/reset', [AuthController::class, 'resetPassword']);
    Route::post('password/validate-token', [AuthController::class, 'validateToken']);
});

Route::post("/telegram/receive-message", [TelegramController::class, "receiveMessage"]);
Route::post("/telegram/{bot_id}/receive", [TelegramController::class, "receiveCustomMessage"]);

// WhatsApp webhook routes (consolidated)
Route::get("/whatsapp/webhook", [WhatsAppWebhookController::class, "verify"]);
Route::post("/whatsapp/webhook", [WhatsAppWebhookController::class, "handle"]);

Route::middleware(['auth:sanctum'])->group(function () {
    // User routes
    Route::get("/user", [AuthController::class, "get"]);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/logout_all_sessions', [AuthController::class, 'logoutAllSessions']);
    Route::delete("/user/delete", [AuthController::class, "delete"]);

    Route::get("/whatsapp/testWhatsAppToken/{phone_number_id}", [WhatsAppWebhookController::class, "testWhatsAppToken"]);

    Route::apiResource('users', UserController::class);
    Route::apiResource('profiles', ProfileController::class);
    Route::apiResource('organizations', OrganizationController::class);
    Route::apiResource('brands', BrandController::class);
    Route::apiResource('products', ProductController::class);
    Route::get('/clients/{id}/messages', [ClientController::class, 'getWithMessages']);
    Route::apiResource('clients', ClientController::class);
    Route::apiResource('projects', ProjectController::class);
    Route::apiResource('budgets', BudgetController::class);
    Route::apiResource('batches', BatchController::class);

    Route::apiResource('stock_entries', StockEntryController::class);
    Route::apiResource('stock_exits', StockExitController::class);
    Route::apiResource('stocks', StockController::class);
    Route::apiResource('groups', GroupController::class);
    Route::apiResource('shops', ShopController::class);
    Route::apiResource('sales', SaleController::class);
    Route::apiResource('items', ItemController::class);
    Route::apiResource('departments', DepartmentController::class);
    Route::apiResource('department_users', DepartmentUserController::class);

    Route::apiResource('groups_products', GroupProductController::class);
    Route::apiResource('budgets_products', BudgetProductController::class);
    Route::apiResource('projects_products', ProjectProductController::class);
    Route::apiResource('products_histories', ProductHistoryController::class);
    Route::apiResource('custom_products', CustomProductController::class);

    Route::apiResource('telegram_users', TelegramUserController::class);
    Route::apiResource('telegram_bots', TelegramBotController::class);
    Route::apiResource('telegram_chats', TelegramChatController::class);
    Route::apiResource('telegram_messages', TelegramMessageController::class);

    Route::apiResource('flows', FlowController::class);
    Route::apiResource('steps', StepController::class);
    Route::apiResource('bodies', ComponentController::class); // DEPRECATED
    Route::apiResource('components', ComponentController::class);
    Route::apiResource('buttons', ButtonController::class);
    Route::apiResource('campaigns', CampaignController::class);
    Route::apiResource('messages', MessageController::class);
    Route::apiResource('interactions', InteractionController::class);
    Route::apiResource('conversations', ConversationController::class);
    Route::apiResource('templates', TemplateController::class);
    Route::apiResource('parameters', ParameterController::class);
    Route::apiResource('phone_numbers', PhoneNumberController::class);
    Route::apiResource('whatsapp_messages', WhatsAppMessageController::class);
    Route::apiResource('whatsapp_webhook_entries', WhatsAppWebhookEntryController::class)->only(['index', 'show']);

    Route::post('/flow/save', [FlowController::class, 'saveFullFlow']);

    Route::post('/template/save', [TemplateController::class, 'saveFullTemplate']);
    Route::post('/template/publish/whatsapp/{id}', [TemplateController::class, 'publishToWhatsapp']);
    Route::post('/template/republish/whatsapp/{id}', [TemplateController::class, 'rePublishToWhatsapp']);

    Route::get('/template/get-to-whatsapp-payload/{id}', [TemplateController::class, 'getToWhatsAppPayload']);
    Route::get('/component/get-to-whatsapp-payload/{id}', [ComponentController::class, 'getToWhatsAppPayload']);
    Route::get('/message/get-to-whatsapp-payload/{id}', [MessageController::class, 'getToWhatsAppPayload']);

    Route::post('/message/generate-messages/{campaign_id}', [MessageController::class, 'generateMessages']);

    Route::post('/campaign/add-clients/{id}', [CampaignController::class, 'addClients']);
    Route::post('/campaign/remove-client/{id}', [CampaignController::class, 'removeClient']);
    Route::post('/campaign/launch/{id}', [CampaignController::class, 'launch']);

    Route::get('/campaign/{id}/clients', [CampaignController::class, 'getClients']);

    // Category and Tag management routes
    Route::apiResource('categories', CategoryController::class);
    Route::get('/tags', [TagController::class, 'index']);
    Route::get('/tags/most-used', [TagController::class, 'mostUsed']);
    Route::get('/tags/suggestions', [TagController::class, 'suggestions']);

    // Campaign categorization and tagging routes
    Route::post('/campaign/{id}/categories', [CampaignController::class, 'assignCategories']);
    Route::post('/campaign/{id}/tags', [CampaignController::class, 'assignTags']);

    // Campaign status management routes
    Route::post('/campaign/{id}/cancel', [CampaignController::class, 'cancel']);
    Route::get('/campaign/{id}/status-history', [CampaignController::class, 'statusHistory']);
    Route::get('/campaign/{id}/status-timeline', [CampaignController::class, 'statusTimeline']);

    // Message delivery and retry routes
    Route::get('/campaign/{id}/messages', [MessageController::class, 'getByCampaign']);
    Route::get('/campaign/{id}/messages/failed', [MessageController::class, 'getFailedByCampaign']);
    Route::get('/campaign/{id}/messages/statistics', [MessageController::class, 'getCampaignStatistics']);
    Route::post('/campaign/{id}/messages/resend-failed', [MessageController::class, 'resendFailedByCampaign']);
    Route::post('/message/{id}/resend', [MessageController::class, 'resend']);
    Route::get('/message/{id}/delivery-status', [MessageController::class, 'getDeliveryStatus']);

    // WhatsApp synchronization routes
    Route::post('/whatsapp/sync/message/{id}', [WhatsAppSyncController::class, 'syncMessage']);
    Route::post('/whatsapp/sync/campaign/{id}', [WhatsAppSyncController::class, 'syncCampaign']);
    Route::get('/whatsapp/sync/logs', [WhatsAppSyncController::class, 'getLogs']);
    Route::get('/whatsapp/sync/entity-logs', [WhatsAppSyncController::class, 'getEntityLogs']);
    Route::get('/whatsapp/sync/trends', [WhatsAppSyncController::class, 'getTrends']);
    Route::get('/whatsapp/sync/status-overview', [WhatsAppSyncController::class, 'getStatusOverview']);
    Route::post('/whatsapp/sync/trigger-proactive', [WhatsAppSyncController::class, 'triggerProactiveSync']);

    // Analytics and metrics routes
    Route::get('/analytics/dashboard', [AnalyticsController::class, 'getDashboard']);
    Route::get('/analytics/campaign/{id}', [AnalyticsController::class, 'getCampaignAnalytics']);
    Route::post('/analytics/campaigns/multiple', [AnalyticsController::class, 'getMultipleCampaignAnalytics']);
    Route::post('/analytics/campaigns/compare', [AnalyticsController::class, 'getPerformanceComparison']);
    Route::post('/analytics/engagement/record', [AnalyticsController::class, 'recordEngagementEvent']);
    Route::post('/analytics/engagement/bulk', [AnalyticsController::class, 'recordBulkEngagementEvents']);
    Route::get('/analytics/message/{id}/engagement', [AnalyticsController::class, 'getMessageEngagementSummary']);
    Route::post('/analytics/trigger-calculation', [AnalyticsController::class, 'triggerCalculation']);

    Route::get('/debug/message/getMessagesAvailableToSent', [MessageController::class, 'getMessagesAvailableToSent']);

    Route::apiResource('imports', ImportController::class);
    Route::post('/import/{id}/process', [ImportController::class, 'process']);

    Route::post('/project/budget/{budget_id}', [ProjectController::class, 'projectFromBudget']);
    Route::post('/project/{id}/products/', [ProjectController::class, 'attachProducts']);
    Route::post('/budget/{id}/products/', [BudgetController::class, 'attachProducts']);

    Route::get("reports/stock_entries", [ReportController::class, 'stock_entries']);
    Route::get("reports/stock_exits", [ReportController::class, 'stock_exits']);

    Route::post('/batch/{id}/process-at-stock/', [BatchController::class, 'addBatchToStock']);

    Route::get("logs/fetch/{id}", [LogController::class, 'fetch']);
    Route::get("logs/fetch_from_organization/{organization_id}", [LogController::class, 'fetchFromOrganization']);
    Route::get("logs/fetch_all", [LogController::class, 'fetchAll']);

    Route::post('/ocr/image-reader/', [RawImageController::class, 'read']);

    // COUNTS and SUMS
    Route::get("reports/{model}/count", [ReportController::class, 'count']);
    Route::get("reports/{model}/sum/{column}", [ReportController::class, 'sum']);

    // Notifications
    Route::get('/notifications/unread', [UserController::class, 'getUnreadNotifications']);
    Route::get('/notifications/all', [UserController::class, 'getAllNotifications']);
    Route::post('/notifications/read', [UserController::class, 'readAllNotifications']);
    Route::post('/notifications/read-one/{$id}', [UserController::class, 'readNotification']);

    // Subscription Routes (no prefix)
    Route::get('/organization/{id}/check-access', [OrganizationController::class, 'checkAccess']);
    Route::get('/organization/{id}/check-asaas-integration', [OrganizationController::class, 'checkAsaasIntegration']);
    Route::post('/subscriptions', [SubscriptionController::class, 'store']);
    Route::get('/subscriptions/{id}', [SubscriptionController::class, 'show']);
    Route::put('/subscriptions/{id}', [SubscriptionController::class, 'update']);
    Route::get('/subscriptions/organization/{organizationId}', [SubscriptionController::class, 'getByOrganization']);
    Route::post('/subscriptions/grant-courtesy', [SubscriptionController::class, 'grantCourtesy']);
    Route::delete('/subscriptions/revoke-courtesy/{organizationId}', [SubscriptionController::class, 'revokeCourtesy']);

    // ASAAS Organization Routes (no prefix)
    Route::get('/organization/{id}/get-to-asaas-payload', [OrganizationController::class, 'getToASAASPayload']);

    // ASAAS Account Routes
    Route::get('/asaas/account/my-account', [AsaasAccountController::class, 'getMyAccount']);
    Route::put('/asaas/account/my-account', [AsaasAccountController::class, 'updateMyAccount']);
    Route::get('/asaas/account/balance', [AsaasAccountController::class, 'getBalance']);
    Route::get('/asaas/account/statistics', [AsaasAccountController::class, 'getStatistics']);
    Route::post('/asaas/account/subaccount', [AsaasAccountController::class, 'createSubaccount']);
    Route::get('/asaas/account/subaccounts', [AsaasAccountController::class, 'getSubaccounts']);
    Route::get('/asaas/account/subaccount', [AsaasAccountController::class, 'getSubaccount']);
    Route::get('/asaas/account/subaccount/search', [AsaasAccountController::class, 'searchSubaccount']);
    Route::get('/asaas/account/organization/sync', [AsaasAccountController::class, 'syncOrganization']);
    Route::put('/asaas/account/subaccount', [AsaasAccountController::class, 'updateSubaccount']);
    Route::delete('/asaas/account/subaccount', [AsaasAccountController::class, 'deleteSubaccount']);
    Route::delete('/asaas/account/my-account/subaccount', [AsaasAccountController::class, 'deleteMyAccountSubaccount']);

    // ASAAS Customer Routes
    Route::post('/asaas/customer', [AsaasCustomerController::class, 'create']);
    Route::get('/asaas/customers', [AsaasCustomerController::class, 'index']);
    Route::get('/asaas/customer/{client_id}', [AsaasCustomerController::class, 'show']);
    Route::put('/asaas/customer/{client_id}', [AsaasCustomerController::class, 'update']);
    Route::delete('/asaas/customer/{client_id}', [AsaasCustomerController::class, 'destroy']);
    Route::get('/asaas/org-customer/{organization_id}', [AsaasCustomerController::class, 'showOrganization']);
    Route::put('/asaas/org-customer/{organization_id}', [AsaasCustomerController::class, 'updateOrganization']);
    Route::delete('/asaas/org-customer/{organization_id}', [AsaasCustomerController::class, 'destroyOrganization']);

    Route::get('/asaas/customer/search/email', [AsaasCustomerController::class, 'searchByEmail']);
    Route::get('/asaas/customer/search/document', [AsaasCustomerController::class, 'searchByDocument']);
    Route::get('/asaas/customer/{client_id}/notifications', [AsaasCustomerController::class, 'getNotifications']);
    Route::get('/asaas/org-customer/{organization_id}/notifications', [AsaasCustomerController::class, 'getOrgNotifications']);

    // ASAAS Payment Routes
    Route::post('/asaas/payment', [AsaasPaymentController::class, 'create']);
    Route::get('/asaas/payments', [AsaasPaymentController::class, 'index']);
    Route::get('/asaas/payment/{payment_id}', [AsaasPaymentController::class, 'show']);
    Route::put('/asaas/payment/{payment_id}', [AsaasPaymentController::class, 'update']);
    Route::delete('/asaas/payment/{payment_id}', [AsaasPaymentController::class, 'destroy']);

    // ASAAS Subscription Routes
    Route::post('/asaas/subscription', [AsaasSubscriptionController::class, 'create']);
    Route::get('/asaas/subscriptions', [AsaasSubscriptionController::class, 'index']);
    Route::get('/asaas/subscription/{subscription_id}', [AsaasSubscriptionController::class, 'show']);
    Route::put('/asaas/subscription/{subscription_id}', [AsaasSubscriptionController::class, 'update']);
    Route::delete('/asaas/subscription/{subscription_id}', [AsaasSubscriptionController::class, 'destroy']);
    Route::get('/asaas/subscription/{subscription_id}/payments', [AsaasSubscriptionController::class, 'getPayments']);

    // ASAAS Resources Routes - for manipulating our database records directly
    // AsaasOrganization Resource Routes
    Route::get('/asaas-resources/organizations', [AsaasOrganizationResourceController::class, 'index']);
    Route::post('/asaas-resources/organizations', [AsaasOrganizationResourceController::class, 'store']);
    Route::get('/asaas-resources/organizations/{id}', [AsaasOrganizationResourceController::class, 'show']);
    Route::put('/asaas-resources/organizations/{id}', [AsaasOrganizationResourceController::class, 'update']);
    Route::delete('/asaas-resources/organizations/{id}', [AsaasOrganizationResourceController::class, 'destroy']);

    // AsaasOrganizationCustomer Resource Routes
    Route::get('/asaas-resources/organization-customers', [AsaasOrganizationCustomerResourceController::class, 'index']);
    Route::post('/asaas-resources/organization-customers', [AsaasOrganizationCustomerResourceController::class, 'store']);
    Route::get('/asaas-resources/organization-customers/{id}', [AsaasOrganizationCustomerResourceController::class, 'show']);
    Route::put('/asaas-resources/organization-customers/{id}', [AsaasOrganizationCustomerResourceController::class, 'update']);
    Route::delete('/asaas-resources/organization-customers/{id}', [AsaasOrganizationCustomerResourceController::class, 'destroy']);

    // AsaasClient Resource Routes
    Route::get('/asaas-resources/clients', [AsaasClientResourceController::class, 'index']);
    Route::post('/asaas-resources/clients', [AsaasClientResourceController::class, 'store']);
    Route::get('/asaas-resources/clients/{id}', [AsaasClientResourceController::class, 'show']);
    Route::put('/asaas-resources/clients/{id}', [AsaasClientResourceController::class, 'update']);
    Route::delete('/asaas-resources/clients/{id}', [AsaasClientResourceController::class, 'destroy']);

    // AsaasSale Resource Routes
    Route::get('/asaas-resources/sales', [AsaasSaleResourceController::class, 'index']);
    Route::post('/asaas-resources/sales', [AsaasSaleResourceController::class, 'store']);
    Route::get('/asaas-resources/sales/{id}', [AsaasSaleResourceController::class, 'show']);
    Route::put('/asaas-resources/sales/{id}', [AsaasSaleResourceController::class, 'update']);
    Route::delete('/asaas-resources/sales/{id}', [AsaasSaleResourceController::class, 'destroy']);

    // AsaasSubscription Resource Routes
    Route::get('/asaas-resources/subscriptions', [AsaasSubscriptionResourceController::class, 'index']);
    Route::post('/asaas-resources/subscriptions', [AsaasSubscriptionResourceController::class, 'store']);
    Route::get('/asaas-resources/subscriptions/{id}', [AsaasSubscriptionResourceController::class, 'show']);
    Route::put('/asaas-resources/subscriptions/{id}', [AsaasSubscriptionResourceController::class, 'update']);
    Route::delete('/asaas-resources/subscriptions/{id}', [AsaasSubscriptionResourceController::class, 'destroy']);

    // WhatsApp Webhook Log API routes
    Route::apiResource('whatsapp-webhook-logs', WhatsAppWebhookLogController::class);
    Route::get('whatsapp-webhook-logs/recent/list', [WhatsAppWebhookLogController::class, 'recent']);
    Route::get('whatsapp-webhook-logs/event-type/{eventType}', [WhatsAppWebhookLogController::class, 'byEventType']);
    Route::get('whatsapp-webhook-logs/status/{status}', [WhatsAppWebhookLogController::class, 'byProcessingStatus']);
    Route::get('whatsapp-webhook-logs/statistics/summary', [WhatsAppWebhookLogController::class, 'statistics']);

});

