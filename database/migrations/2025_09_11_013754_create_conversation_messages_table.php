<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversation_messages', function (Blueprint $table) {
            $table->id();

            // Foreign keys
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('phone_number_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('conversation_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('webhook_log_id')->nullable()->constrained('whatsapp_webhook_logs')->onDelete('set null');
            $table->foreignId('message_id')->nullable()->constrained()->onDelete('set null');

            // Message direction flags
            $table->boolean('inbound')->default(false);
            $table->boolean('outbound')->default(false);

            // Message content
            $table->text('message');

            // Timing
            $table->timestamp('sent_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['organization_id', 'client_id', 'created_at']);
            $table->index(['conversation_id', 'created_at']);
            $table->index(['inbound', 'outbound']);

            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversation_messages');
    }
};
