<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Step>
 */
class StepFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'flow_id' => \App\Models\Flow::factory(),
            'step' => fake()->numberBetween(1, 10),
            'type' => fake()->randomElement(['text', 'image', 'video', 'document', 'input']),
            'position' => fake()->numberBetween(1, 10),
            'next_step' => null,
            'earlier_step' => null,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'is_message' => false,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => false,
            'input' => null,
            'json' => '{}',
        ];
    }

    /**
     * Create a first step.
     */
    public function first(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_initial_step' => true,
            'step' => 1,
            'position' => 1,
            'earlier_step' => null,
        ]);
    }

    /**
     * Create a last step.
     */
    public function last(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_ending_step' => true,
            'next_step' => null,
        ]);
    }

    /**
     * Create an input step.
     */
    public function input(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_input' => true,
            'type' => 'input',
            'input' => fake()->randomElement(['client.name', 'client.email', 'client.phone', 'client.address']),
        ]);
    }

    /**
     * Create an interactive step.
     */
    public function interactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_interactive' => true,
            'is_message' => true,
        ]);
    }

    /**
     * Create a message step.
     */
    public function message(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_message' => true,
        ]);
    }

    /**
     * Create a condition step (for backward compatibility).
     */
    public function condition(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_interactive' => true,
            'is_message' => true,
        ]);
    }

    /**
     * Create a step with buttons (for backward compatibility).
     */
    public function withButtons(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_interactive' => true,
            'is_message' => true,
        ]);
    }
}
