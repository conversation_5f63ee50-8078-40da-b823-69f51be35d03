<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\StepFactory;
use App\Domains\ChatBot\Step;
use App\Models\Step as StepModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StepFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false, false); // without flow and component

        $this->assertInstanceOf(Step::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->flow_id, $domain->flow_id);
        $this->assertEquals($model->type, $domain->type);
        $this->assertEquals($model->step, $domain->step);
        $this->assertEquals($model->next_step, $domain->next_step);
        $this->assertEquals($model->earlier_step, $domain->earlier_step);
        $this->assertEquals($model->is_initial_step, $domain->is_initial_step);
        $this->assertEquals($model->is_ending_step, $domain->is_ending_step);
        $this->assertEquals($model->is_message, $domain->is_message);
        $this->assertEquals($model->is_interactive, $domain->is_interactive);
        $this->assertEquals($model->is_command, $domain->is_command);
        $this->assertEquals($model->is_input, $domain->is_input);
        $this->assertEquals($model->json, $domain->json);
        $this->assertEquals($model->input, $domain->input);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_models_collection()
    {
        $models = collect([
            $this->createModelInstance(),
            $this->createModelInstance(),
            $this->createModelInstance(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels($models, false, false);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Step::class, $domain);
        }
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(StepFactory::class);

        $this->assertInstanceOf(StepFactory::class, $factory);
    }

    public function test_factory_can_handle_different_step_types()
    {
        $firstStep = StepModel::factory()->first()->create();
        $lastStep = StepModel::factory()->last()->create();
        $inputStep = StepModel::factory()->input()->create();
        $conditionStep = StepModel::factory()->condition()->create();
        $buttonStep = StepModel::factory()->withButtons()->create();

        $factory = $this->createFactoryInstance();

        $firstDomain = $factory->buildFromModel($firstStep, false, false);
        $lastDomain = $factory->buildFromModel($lastStep, false, false);
        $inputDomain = $factory->buildFromModel($inputStep, false, false);
        $conditionDomain = $factory->buildFromModel($conditionStep, false, false);
        $buttonDomain = $factory->buildFromModel($buttonStep, false, false);

        $this->assertTrue($firstDomain->is_initial_step);
        $this->assertEquals(1, $firstDomain->step);
        $this->assertNull($firstDomain->earlier_step);

        $this->assertTrue($lastDomain->is_ending_step);
        $this->assertNull($lastDomain->next_step);

        $this->assertTrue($inputDomain->is_input);
        $this->assertEquals('input', $inputDomain->type);
        $this->assertNotNull($inputDomain->input);

        $this->assertTrue($conditionDomain->is_interactive);
        $this->assertTrue($conditionDomain->is_message);

        $this->assertTrue($buttonDomain->is_interactive);
        $this->assertTrue($buttonDomain->is_message);
    }

    public function test_build_from_models_with_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels(collect(), false, false);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve circular dependencies
        return app()->make(StepFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Step::class;
    }

    protected function createModelInstance()
    {
        return StepModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Step\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'flow_id' => 1,
            'step' => '1',
            'type' => 'text',
            'position' => 1,
            'next_step' => null,
            'earlier_step' => null,
            'is_initial_step' => true,
            'is_ending_step' => false,
            'is_message' => true,
            'is_interactive' => false,
            'is_command' => false,
            'is_input' => false,
            'json' => '{"content": "Hello"}',
            'input' => null,
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Step\UpdateRequest();
        $request->merge([
            'organization_id' => 1,
            'flow_id' => 1,
            'step' => '2',
            'type' => 'input',
            'position' => 2,
            'next_step' => null,
            'earlier_step' => 1,
            'is_initial_step' => false,
            'is_ending_step' => true,
            'is_message' => false,
            'is_interactive' => true,
            'is_command' => false,
            'is_input' => true,
            'json' => '{"content": "Please enter your name"}',
            'input' => 'client.name',
        ]);
        return $request;
    }
}
