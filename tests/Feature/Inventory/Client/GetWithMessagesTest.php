<?php

namespace Tests\Feature\Inventory\Client;

use App\Models\Client;
use App\Models\Message;
use App\Models\Organization;
use App\Models\User;
use App\Enums\MessageStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetWithMessagesTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_can_get_client_with_messages()
    {
        // Create some messages for the client
        $message1 = Message::factory()->create([
            'client_id' => $this->client->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_sent,
            'message' => 'First message'
        ]);

        $message2 = Message::factory()->create([
            'client_id' => $this->client->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_draft,
            'message' => 'Second message'
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/clients/{$this->client->id}/messages");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'status',
                'data' => [
                    'client' => [
                        'id',
                        'name',
                        'phone',
                        'email'
                    ],
                    'messages' => [
                        '*' => [
                            'id',
                            'client_id',
                            'message',
                            'status',
                            'created_at'
                        ]
                    ]
                ]
            ]);

        // Verify messages are in descending order by id
        $responseData = $response->json('data');
        $this->assertCount(2, $responseData['messages']);
        $this->assertEquals($message2->id, $responseData['messages'][0]['id']); // Most recent first
        $this->assertEquals($message1->id, $responseData['messages'][1]['id']);
    }

    public function test_cannot_get_client_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherClient = Client::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/clients/{$otherClient->id}/messages");

        $response->assertStatus(500); // Should throw exception for unauthorized access
    }

    public function test_returns_empty_messages_array_when_no_messages()
    {
        $response = $this->actingAs($this->user)
            ->getJson("/api/clients/{$this->client->id}/messages");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertArrayHasKey('client', $responseData);
        $this->assertArrayHasKey('messages', $responseData);
        $this->assertEmpty($responseData['messages']);
    }

    public function test_requires_authentication()
    {
        $response = $this->getJson("/api/clients/{$this->client->id}/messages");

        $response->assertStatus(401);
    }
}
