<?php

namespace Tests\Feature\Inventory\Client;

use App\Models\Client;
use App\Models\Message;
use App\Models\Organization;
use App\Models\User;
use App\Models\Campaign;
use App\Models\Template;
use App\Enums\MessageStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetWithMessagesIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_complete_integration_with_real_data()
    {
        // Create organization
        $organization = Organization::factory()->create([
            'name' => 'Test Organization'
        ]);

        // Create user
        $user = User::factory()->create([
            'organization_id' => $organization->id
        ]);

        // Create client
        $client = Client::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Test Client',
            'phone' => '+5511999999999',
            'email' => '<EMAIL>'
        ]);

        // Create campaign and template for messages
        $campaign = Campaign::factory()->create([
            'organization_id' => $organization->id
        ]);

        $template = Template::factory()->create([
            'organization_id' => $organization->id
        ]);

        // Create multiple messages for the client
        $messages = [];
        for ($i = 1; $i <= 5; $i++) {
            $messages[] = Message::factory()->create([
                'client_id' => $client->id,
                'organization_id' => $organization->id,
                'campaign_id' => $campaign->id,
                'template_id' => $template->id,
                'message' => "Test message {$i}",
                'status' => $i % 2 === 0 ? MessageStatus::is_sent : MessageStatus::is_draft,
                'is_sent' => $i % 2 === 0,
            ]);
        }

        // Make API call
        $response = $this->actingAs($user)
            ->getJson("/api/clients/{$client->id}/messages");

        // Assert response structure
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Authorized'
            ]);

        $responseData = $response->json('data');

        // Verify client data
        $this->assertEquals($client->id, $responseData['client']['id']);
        $this->assertEquals($client->name, $responseData['client']['name']);
        $this->assertEquals($client->phone, $responseData['client']['phone']);
        $this->assertEquals($client->email, $responseData['client']['email']);

        // Verify messages data
        $this->assertCount(5, $responseData['messages']);

        // Verify messages are in descending order by id
        $messageIds = array_column($responseData['messages'], 'id');
        $sortedIds = $messageIds;
        rsort($sortedIds);
        $this->assertEquals($sortedIds, $messageIds, 'Messages should be ordered by ID in descending order');

        // Verify message content
        foreach ($responseData['messages'] as $index => $messageData) {
            $this->assertArrayHasKey('id', $messageData);
            $this->assertArrayHasKey('client_id', $messageData);
            $this->assertArrayHasKey('message', $messageData);
            $this->assertArrayHasKey('status', $messageData);
            $this->assertArrayHasKey('created_at', $messageData);
            $this->assertEquals($client->id, $messageData['client_id']);
        }

        // Verify that the first message is the most recent one
        $firstMessage = $responseData['messages'][0];
        $lastCreatedMessage = $messages[count($messages) - 1];
        $this->assertEquals($lastCreatedMessage->id, $firstMessage['id']);
    }

    public function test_endpoint_with_no_messages()
    {
        $organization = Organization::factory()->create();
        $user = User::factory()->create(['organization_id' => $organization->id]);
        $client = Client::factory()->create(['organization_id' => $organization->id]);

        $response = $this->actingAs($user)
            ->getJson("/api/clients/{$client->id}/messages");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        
        $this->assertArrayHasKey('client', $responseData);
        $this->assertArrayHasKey('messages', $responseData);
        $this->assertEmpty($responseData['messages']);
    }
}
