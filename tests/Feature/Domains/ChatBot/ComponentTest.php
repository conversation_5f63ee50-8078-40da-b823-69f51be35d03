<?php

namespace Tests\Feature\Domains\ChatBot;

use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Parameter;
use App\Domains\Inventory\Client;
use App\Enums\ComponentFormat;
use Tests\TestCase;

class ComponentTest extends TestCase
{
    private function createClient(): Client
    {
        return new Client(
            id: 1,
            organization_id: 1,
            name: '<PERSON>',
            phone: '+5511999887766',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: '123 Main St',
            number: null,
            neighborhood: 'Centro',
            cep: '01234-567',
            complement: null,
            civil_state: null,
            description: null
        );
    }

    public function test_convert_variables_to_placeholders()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body Component',
            type: 'BODY',
            text: 'Hello {{client.name}}, your email is {{client.email}} and phone is {{client.phone}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        $result = $component->convertVariablesToPlaceholders($component->text);

        $this->assertEquals('Hello {{1}}, your email is {{2}} and phone is {{3}}', $result);
        $this->assertEquals(['client.name', 'client.email', 'client.phone'], $component->getVariableMapping());
    }

    public function test_to_whatsapp_template_payload_body()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body Component',
            type: 'BODY',
            text: 'Hello {{client.name}}, welcome to our service!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $payload = $component->toWhatsAppTemplatePayload();

        $this->assertIsArray($payload);
        $this->assertEquals('BODY', $payload['type']);
        $this->assertEquals('Hello {{1}}, welcome to our service!', $payload['text']);
    }

    public function test_to_whatsapp_template_payload_header()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Header Component',
            type: 'HEADER',
            text: 'Welcome {{client.name}}!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $payload = $component->toWhatsAppTemplatePayload();

        $this->assertIsArray($payload);
        $this->assertEquals('HEADER', $payload['type']);
        $this->assertEquals('TEXT', $payload['format']);
        $this->assertEquals('Welcome {{1}}!', $payload['text']);
    }

    public function test_to_whatsapp_template_payload_footer()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Footer Component',
            type: 'FOOTER',
            text: 'Thank you, {{client.name}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        $payload = $component->toWhatsAppTemplatePayload();

        $this->assertIsArray($payload);
        $this->assertEquals('FOOTER', $payload['type']);
        $this->assertEquals('Thank you, {{1}}', $payload['text']);
    }

    public function test_to_whatsapp_message_payload_body()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body Component',
            type: 'BODY',
            text: 'Hello {{client.name}}, your email is {{client.email}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        $client = $this->createClient();
        $availableModels = ['client' => $client];

        $payload = $component->toWhatsAppMessagePayload($availableModels);

        $this->assertIsArray($payload);
        $this->assertEquals('body', $payload['type']);
        $this->assertArrayHasKey('parameters', $payload);
        $this->assertCount(2, $payload['parameters']);

        $this->assertEquals('text', $payload['parameters'][0]['type']);
        $this->assertEquals('John Doe', $payload['parameters'][0]['text']);

        $this->assertEquals('text', $payload['parameters'][1]['type']);
        $this->assertEquals('<EMAIL>', $payload['parameters'][1]['text']);
    }

    public function test_to_whatsapp_message_payload_header()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Header Component',
            type: 'HEADER',
            text: 'Welcome {{client.name}}!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $client = $this->createClient();
        $availableModels = ['client' => $client];

        $payload = $component->toWhatsAppMessagePayload($availableModels);

        $this->assertIsArray($payload);
        $this->assertEquals('header', $payload['type']);
        $this->assertArrayHasKey('parameters', $payload);
        $this->assertCount(1, $payload['parameters']);

        $this->assertEquals('text', $payload['parameters'][0]['type']);
        $this->assertEquals('John Doe', $payload['parameters'][0]['text']);
    }

    public function test_to_whatsapp_message_payload_footer()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Footer Component',
            type: 'FOOTER',
            text: 'Contact us at {{client.phone}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        $client = $this->createClient();
        $availableModels = ['client' => $client];

        $payload = $component->toWhatsAppMessagePayload($availableModels);

        $this->assertIsArray($payload);
        $this->assertEquals('footer', $payload['type']);
        $this->assertArrayHasKey('parameters', $payload);
        $this->assertCount(1, $payload['parameters']);

        $this->assertEquals('text', $payload['parameters'][0]['type']);
        $this->assertEquals('+5511999887766', $payload['parameters'][0]['text']);
    }

    public function test_to_whatsapp_message_payload_no_variables()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body Component',
            type: 'BODY',
            text: 'Hello, welcome to our service!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $payload = $component->toWhatsAppMessagePayload([]);

        $this->assertIsArray($payload);
        $this->assertEquals('body', $payload['type']);
        $this->assertArrayNotHasKey('parameters', $payload);
    }

    public function test_to_whatsapp_message_payload_invalid_type()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Invalid Component',
            type: 'INVALID',
            text: 'Hello {{client.name}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        $client = $this->createClient();
        $availableModels = ['client' => $client];

        $payload = $component->toWhatsAppMessagePayload($availableModels);

        $this->assertEquals([], $payload);
    }

    public function test_extract_parameters_with_missing_model()
    {
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body Component',
            type: 'BODY',
            text: 'Hello {{client.name}}, your order {{order.id}} is ready',
            format: ComponentFormat::TEXT,
            json: null
        );

        $client = $this->createClient();
        $availableModels = ['client' => $client]; // Missing 'order' model

        $payload = $component->toWhatsAppMessagePayload($availableModels);

        $this->assertIsArray($payload);
        $this->assertEquals('body', $payload['type']);
        $this->assertArrayHasKey('parameters', $payload);
        $this->assertCount(2, $payload['parameters']);

        $this->assertEquals('John Doe', $payload['parameters'][0]['text']);
        $this->assertEquals('', $payload['parameters'][1]['text']); // Missing model returns empty string
    }
}
