<?php

namespace Tests\Feature\UseCases\ChatBot;

use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Template;
use App\Domains\Inventory\Client;
use App\Enums\ComponentFormat;
use App\Enums\MessageStatus;
use Carbon\Carbon;
use Tests\TestCase;

class MessageGetToWhatsAppPayloadTest extends TestCase
{
    public function test_message_get_to_whatsapp_payload_endpoint_returns_correct_format()
    {
        // Create test data directly without factories to avoid circular dependencies
        $client = $this->createClient();
        $template = $this->createTemplate();

        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: 1,
            client_id: 1,
            message: 'Template message',
            status: MessageStatus::is_draft,
            client: $client,
            template: $template
        );

        // Test the toWhatsAppTemplatePayload method directly
        $payload = $message->toWhatsAppTemplatePayload();

        // Verify the payload structure matches WhatsApp requirements
        $this->assertIsArray($payload);
        $this->assertEquals('whatsapp', $payload['messaging_product']);
        $this->assertEquals($client->phone, $payload['to']);
        $this->assertEquals('template', $payload['type']);

        // Verify template structure
        $this->assertArrayHasKey('template', $payload);
        $this->assertEquals($template->name, $payload['template']['name']);
        $this->assertArrayHasKey('language', $payload['template']);
        $this->assertEquals(['code' => $template->language], $payload['template']['language']);
        $this->assertArrayHasKey('components', $payload['template']);

        // Verify components structure
        $components = $payload['template']['components'];
        $this->assertIsArray($components);
        $this->assertCount(3, $components); // header, body, footer

        // Verify header component
        $headerComponent = $components[0];
        $this->assertEquals('header', $headerComponent['type']);
        $this->assertArrayHasKey('parameters', $headerComponent);
        $this->assertCount(1, $headerComponent['parameters']);
        $this->assertEquals('text', $headerComponent['parameters'][0]['type']);
        $this->assertEquals('John Doe', $headerComponent['parameters'][0]['text']);

        // Verify body component
        $bodyComponent = $components[1];
        $this->assertEquals('body', $bodyComponent['type']);
        $this->assertArrayHasKey('parameters', $bodyComponent);
        $this->assertCount(2, $bodyComponent['parameters']);
        $this->assertEquals('text', $bodyComponent['parameters'][0]['type']);
        $this->assertEquals('John Doe', $bodyComponent['parameters'][0]['text']);
        $this->assertEquals('text', $bodyComponent['parameters'][1]['type']);
        $this->assertEquals('<EMAIL>', $bodyComponent['parameters'][1]['text']);

        // Verify footer component
        $footerComponent = $components[2];
        $this->assertEquals('footer', $footerComponent['type']);
        $this->assertArrayHasKey('parameters', $footerComponent);
        $this->assertCount(1, $footerComponent['parameters']);
        $this->assertEquals('text', $footerComponent['parameters'][0]['type']);
        $this->assertEquals('John Doe', $footerComponent['parameters'][0]['text']);
    }

    public function test_message_payload_respects_whatsapp_format_requirements()
    {
        $client = $this->createClient();
        $template = $this->createTemplate();

        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: 1,
            client_id: 1,
            message: 'Template message',
            status: MessageStatus::is_draft,
            client: $client,
            template: $template
        );

        $payload = $message->toWhatsAppTemplatePayload();

        // Verify exact format matches the required WhatsApp structure
        $expectedStructure = [
            'messaging_product' => 'whatsapp',
            'to' => '+5579981166640',
            'type' => 'template',
            'template' => [
                'name' => 'welcome_template',
                'language' => [
                    'code' => 'pt_BR'
                ],
                'components' => [
                    [
                        'type' => 'header',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'text' => 'John Doe'
                            ]
                        ]
                    ],
                    [
                        'type' => 'body',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'text' => 'John Doe'
                            ],
                            [
                                'type' => 'text',
                                'text' => '<EMAIL>'
                            ]
                        ]
                    ],
                    [
                        'type' => 'footer',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'text' => 'John Doe'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $this->assertEquals($expectedStructure, $payload);
    }

    private function createClient(): Client
    {
        return new Client(
            id: 1,
            organization_id: 1,
            name: 'John Doe',
            phone: '+5579981166640',
            email: '<EMAIL>',
            profession: 'Developer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'Single',
            description: 'Test client',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createPhoneNumber(): PhoneNumber
    {
        return new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: null,
            flow_id: null,
            phone_number: '+5579981166640',
            name: 'Test Phone',
            description: 'Test phone number',
            is_active: true,
            whatsapp_phone_number_id: 'test_phone_id',
            whatsapp_business_id: 'test_business_id',
            whatsapp_access_token: 'test_token',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createTemplate(): Template
    {
        // Create header component with variable
        $headerComponent = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Header',
            type: 'HEADER',
            sub_type: null,
            index: 0,
            text: 'Welcome {{client.name}}!',
            format: ComponentFormat::TEXT,
            json: null
        );

        // Create body component with multiple variables
        $bodyComponent = new Component(
            id: 2,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body',
            type: 'BODY',
            sub_type: null,
            index: 1,
            text: 'Hello {{client.name}}, your email is {{client.email}}.',
            format: ComponentFormat::TEXT,
            json: null
        );

        // Create footer component with variable
        $footerComponent = new Component(
            id: 3,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Footer',
            type: 'FOOTER',
            sub_type: null,
            index: 2,
            text: 'Thanks, {{client.name}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        return new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: null,
            client_id: null,
            name: 'welcome_template',
            category: 'UTILITY',
            parameter_format: null,
            language: 'pt_BR',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            components: [$headerComponent, $bodyComponent, $footerComponent],
            phone_number: $this->createPhoneNumber()
        );
    }
}
